import asyncio
import threading
import requests
import json
from concurrent.futures import ThreadPoolExecutor
from flask import Flask, request, jsonify
from typing import Dict, List, Any
from config import Config

app = Flask(__name__)

def get_headers(model_key: str) -> Dict[str, str]:
    """
    Generate appropriate headers for each model
    """
    config = Config.MODEL_CONFIGS.get(model_key)
    if not config:
        return {}
    
    headers = {
        "Content-Type": "application/json"
    }
    
    if model_key in ["qwen", "summary", "deepseek", "kimi", "classifier"]:
        headers["Authorization"] = f"Bearer {config['api_key']}"
    elif model_key == "zhipu":
        headers["Authorization"] = f"Bearer {config['api_key']}"
    elif model_key == "baidu":
        # Bai<PERSON> may need token in URL rather than header
        pass
    
    return headers

def call_model(model_key: str, prompt: str) -> Dict[str, Any]:
    """
    Call a specific model with the given prompt
    """
    config = Config.MODEL_CONFIGS.get(model_key)
    if not config:
        return {"model": model_key, "error": "Model configuration not found"}
    
    try:
        # Prepare the request payload
        payload = {
            "model": config["model"],
            "messages": [{"role": "user", "content": prompt}]
        }
        
        # For Baidu, we might need to handle token in URL
        url = config["api_url"]
        if model_key == "baidu":
            # Add access token to URL for Baidu
            url = f"{config['api_url']}?access_token={config['api_key']}"
        
        # Make the API call
        response = requests.post(
            url,
            headers=get_headers(model_key),
            data=json.dumps(payload),
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            # Handle different response formats
            content = ""
            if model_key in ["qwen", "summary", "deepseek", "kimi", "classifier"]:
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            elif model_key == "zhipu":
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            elif model_key == "baidu":
                content = result.get("result", "")
            
            return {
                "model": config["name"],
                "response": content,
                "status": "success"
            }
        else:
            return {
                "model": config["name"],
                "error": f"API call failed with status {response.status_code}",
                "response": response.text,
                "status": "error",
                "request_payload": payload  # Include payload for debugging
            }
            
    except Exception as e:
        return {
            "model": config["name"],
            "error": str(e),
            "status": "error"
        }

def classify_question(question: str) -> str:
    """
    Use a small model to classify the question type
    """
    # Build the classification prompt
    prompt = Config.CLASSIFIER_PROMPT.format(question=question)
    
    # Call the classifier model
    result = call_model("classifier", prompt)
    
    if result.get("status") == "success":
        # Extract the classification result
        classification = result.get("response", "").strip().lower()
        # Validate that it's a known category
        if classification in Config.STRATEGY_CONFIG:
            return classification
    
    # Default to "default" if classification fails
    return "default"

def get_models_for_question(question: str, question_type: str = None) -> List[str]:
    """
    Determine which models to use based on question type
    If question_type is not provided, automatically classify the question
    """
    if question_type and question_type in Config.STRATEGY_CONFIG:
        return Config.STRATEGY_CONFIG[question_type]
    else:
        # Automatically classify the question
        auto_type = classify_question(question)
        return Config.STRATEGY_CONFIG[auto_type]

def summarize_responses(question: str, responses: List[Dict[str, Any]]) -> str:
    """
    Summarize the responses from multiple models
    """
    # Build a prompt for the summary model
    response_texts = []
    for resp in responses:
        if resp.get("status") == "success":
            response_texts.append(f"模型 {resp['model']} 的回答:\n{resp['response']}\n")
        else:
            response_texts.append(f"模型 {resp['model']} 出现错误: {resp.get('error', 'Unknown error')}\n")
    
    summary_prompt = f"请根据以下多个模型对问题 '{question}' 的回答进行总结:\n\n" + "\n".join(response_texts) + "\n请给出一个综合性的总结回答:"
    
    # Call the summary model
    summary_result = call_model("summary", summary_prompt)
    if summary_result.get("status") == "success":
        return summary_result.get("response", "")
    else:
        # Return failure reason
        error_msg = summary_result.get("error", "Unknown error")
        return f"总结生成失败: {error_msg}"

@app.route('/ask', methods=['POST'])
@app.route('/ask/<question_type>', methods=['POST'])
def ask_question(question_type: str = None):
    """
    Ask a question to multiple models based on strategy configuration
    """
    data = request.get_json()
    question = data.get('question')
    
    if not question:
        return jsonify({"error": "Missing question in request body"}), 400
    
    # Determine which models to use
    model_keys = get_models_for_question(question, question_type)
    
    # Use ThreadPoolExecutor to call models concurrently
    with ThreadPoolExecutor(max_workers=len(model_keys)) as executor:
        # Submit all model calls
        future_to_model = {
            executor.submit(call_model, model_key, question): model_key 
            for model_key in model_keys
        }
        
        # Collect results
        responses = []
        for future in future_to_model:
            try:
                result = future.result()
                responses.append(result)
            except Exception as e:
                model_key = future_to_model[future]
                responses.append({
                    "model": Config.MODEL_CONFIGS.get(model_key, {}).get("name", model_key),
                    "error": str(e),
                    "status": "error"
                })
    
    # Generate summary
    summary = summarize_responses(question, responses)
    
    return jsonify({
        "question": question,
        "question_type": question_type or "auto",
        "models_used": model_keys,
        "responses": responses,
        "summary": summary
    })

@app.route('/models', methods=['GET'])
def list_models():
    """
    List all available models
    """
    models = {k: v["name"] for k, v in Config.MODEL_CONFIGS.items() if k not in ["summary", "classifier"]}
    return jsonify(models)

@app.route('/strategies', methods=['GET'])
def list_strategies():
    """
    List all available strategies
    """
    return jsonify(Config.STRATEGY_CONFIG)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)