#!/usr/bin/env python3
"""
Test script for the Multi-Model Question Answering MCP Server
"""

import sys
import os
import json
from unittest.mock import patch, MagicMock

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    try:
        from mcp_server import (
            mcp, QuestionRequest, ModelResponse, MultiModelResponse,
            get_headers, call_model, classify_question, 
            get_models_for_question, summarize_responses
        )
        from config import Config
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_config():
    """Test that configuration is properly loaded."""
    print("\nTesting configuration...")
    try:
        from config import Config
        
        # Check that model configs exist
        assert len(Config.MODEL_CONFIGS) > 0, "No model configurations found"
        print(f"✅ Found {len(Config.MODEL_CONFIGS)} model configurations")
        
        # Check that strategy configs exist
        assert len(Config.STRATEGY_CONFIG) > 0, "No strategy configurations found"
        print(f"✅ Found {len(Config.STRATEGY_CONFIG)} strategy configurations")
        
        # Check that classifier prompt exists
        assert Config.CLASSIFIER_PROMPT, "Classifier prompt not found"
        print("✅ Classifier prompt found")
        
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_model_functions():
    """Test core model functions with mocked API calls."""
    print("\nTesting model functions...")
    try:
        from mcp_server import get_headers, call_model, classify_question
        from config import Config
        
        # Test get_headers
        headers = get_headers("qwen")
        assert "Content-Type" in headers, "Content-Type header missing"
        print("✅ get_headers function works")
        
        # Mock the requests.post call for testing
        with patch('mcp_server.requests.post') as mock_post:
            # Mock successful response
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{"message": {"content": "Test response"}}]
            }
            mock_post.return_value = mock_response
            
            # Test call_model
            result = call_model("qwen", "Test question")
            assert result["status"] == "success", "call_model should return success"
            assert "response" in result, "call_model should return response"
            print("✅ call_model function works")
            
            # Test classify_question
            classification = classify_question("How to write Python code?")
            assert classification in Config.STRATEGY_CONFIG, f"Invalid classification: {classification}"
            print("✅ classify_question function works")
        
        return True
    except Exception as e:
        print(f"❌ Model function error: {e}")
        return False

def test_mcp_tools():
    """Test MCP tools with mocked API calls."""
    print("\nTesting MCP tools...")
    try:
        from mcp_server import (
            ask_question, classify_question_tool, call_single_model,
            list_available_models, list_question_strategies
        )
        
        # Test list_available_models
        models = list_available_models()
        assert isinstance(models, dict), "list_available_models should return dict"
        assert len(models) > 0, "Should have available models"
        print("✅ list_available_models works")
        
        # Test list_question_strategies
        strategies = list_question_strategies()
        assert isinstance(strategies, dict), "list_question_strategies should return dict"
        assert len(strategies) > 0, "Should have strategies"
        print("✅ list_question_strategies works")
        
        # Test classify_question_tool with mocked API
        with patch('mcp_server.requests.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [{"message": {"content": "coding"}}]
            }
            mock_post.return_value = mock_response
            
            classification = classify_question_tool("Write Python code")
            assert classification in ["technical", "creative", "analytical", "coding", "long_text", "default"]
            print("✅ classify_question_tool works")
        
        return True
    except Exception as e:
        print(f"❌ MCP tools error: {e}")
        return False

def test_mcp_resources():
    """Test MCP resources."""
    print("\nTesting MCP resources...")
    try:
        from mcp_server import (
            get_models_config, get_strategies_config, 
            get_classifier_prompt, get_models_status
        )
        
        # Test get_models_config
        models_config = get_models_config()
        assert isinstance(models_config, str), "get_models_config should return string"
        config_data = json.loads(models_config)
        assert len(config_data) > 0, "Models config should not be empty"
        print("✅ get_models_config works")
        
        # Test get_strategies_config
        strategies_config = get_strategies_config()
        assert isinstance(strategies_config, str), "get_strategies_config should return string"
        strategies_data = json.loads(strategies_config)
        assert len(strategies_data) > 0, "Strategies config should not be empty"
        print("✅ get_strategies_config works")
        
        # Test get_classifier_prompt
        prompt = get_classifier_prompt()
        assert isinstance(prompt, str), "get_classifier_prompt should return string"
        assert len(prompt) > 0, "Classifier prompt should not be empty"
        print("✅ get_classifier_prompt works")
        
        # Test get_models_status
        status = get_models_status()
        assert isinstance(status, str), "get_models_status should return string"
        status_data = json.loads(status)
        assert len(status_data) > 0, "Models status should not be empty"
        print("✅ get_models_status works")
        
        return True
    except Exception as e:
        print(f"❌ MCP resources error: {e}")
        return False

def test_mcp_prompts():
    """Test MCP prompts."""
    print("\nTesting MCP prompts...")
    try:
        from mcp_server import question_classifier, response_summarizer, model_selector
        
        # Test question_classifier
        prompt = question_classifier("How to code in Python?")
        assert isinstance(prompt, str), "question_classifier should return string"
        assert "How to code in Python?" in prompt, "Question should be in prompt"
        print("✅ question_classifier works")
        
        # Test response_summarizer
        prompt = response_summarizer("Test question", "Test responses")
        assert isinstance(prompt, str), "response_summarizer should return string"
        assert "Test question" in prompt, "Question should be in prompt"
        print("✅ response_summarizer works")
        
        # Test model_selector
        prompt = model_selector("Test question", "coding")
        assert isinstance(prompt, str), "model_selector should return string"
        assert "Test question" in prompt, "Question should be in prompt"
        print("✅ model_selector works")
        
        return True
    except Exception as e:
        print(f"❌ MCP prompts error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Multi-Model Question Answering MCP Server")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_config,
        test_model_functions,
        test_mcp_tools,
        test_mcp_resources,
        test_mcp_prompts
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"❌ Test failed: {test.__name__}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MCP server is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
