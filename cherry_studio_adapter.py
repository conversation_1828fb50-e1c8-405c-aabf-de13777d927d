from flask import Flask, request, jsonify
import requests
import json
import time

app = Flask(__name__)

# MCP服务的地址
MCP_SERVICE_URL = "http://localhost:5000"

@app.route('/v1/chat/completions', methods=['POST'])
def cherry_studio_adapter():
    """
    适配器端点，将CherryStudio的请求转换为对MCP服务的调用
    """
    try:
        # 获取CherryStudio发送的请求数据
        cherry_request = request.get_json()
        
        # 从请求中提取用户消息
        messages = cherry_request.get('messages', [])
        if not messages:
            return jsonify({
                "error": {
                    "message": "No messages provided",
                    "type": "invalid_request_error"
                }
            }), 400
        
        # 提取最后一条用户消息作为问题
        user_message = None
        for msg in reversed(messages):
            if msg.get('role') == 'user':
                user_message = msg.get('content')
                break
        
        if not user_message:
            return jsonify({
                "error": {
                    "message": "No user message found",
                    "type": "invalid_request_error"
                }
            }), 400
        
        # 构造对MCP服务的请求
        mcp_request = {
            "question": user_message
        }
        
        # 发送请求到MCP服务
        mcp_response = requests.post(
            f"{MCP_SERVICE_URL}/ask",
            headers={"Content-Type": "application/json"},
            data=json.dumps(mcp_request)
        )
        
        if mcp_response.status_code != 200:
            return jsonify({
                "error": {
                    "message": f"MCP service error: {mcp_response.text}",
                    "type": "service_error"
                }
            }), 500
        
        # 解析MCP服务的响应
        mcp_data = mcp_response.json()
        
        # 构造符合OpenAI API格式的响应
        summary = mcp_data.get("summary", "")
        if not summary:
            # 如果没有总结，使用第一个成功的模型响应
            for response in mcp_data.get("responses", []):
                if response.get("status") == "success":
                    summary = response.get("response", "")
                    break
        
        openai_response = {
            "id": "chatcmpl-mcp-adapter",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "mcp-multi-model",
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": summary
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
        }
        
        return jsonify(openai_response)
        
    except Exception as e:
        return jsonify({
            "error": {
                "message": f"Adapter error: {str(e)}",
                "type": "adapter_error"
            }
        }), 500

@app.route('/v1/models', methods=['GET'])
def list_models():
    """
    返回一个模型列表，让CherryStudio知道支持哪些模型
    """
    return jsonify({
        "data": [
            {
                "id": "mcp-multi-model",
                "object": "model",
                "owned_by": "mcp-adapter"
            }
        ],
        "object": "list"
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8000, debug=True)