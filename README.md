# Multi-Model Question Answering MCP Server

A Model Context Protocol (MCP) server that aggregates responses from multiple Chinese AI models to provide comprehensive answers to user questions. The server automatically classifies questions and selects the most appropriate combination of models based on the question type.

## What is MCP?

The Model Context Protocol (MCP) is an open protocol developed by Anthropic that enables seamless integration between LLM applications and external data sources and tools. This server implements the MCP standard to provide multi-model AI capabilities as a standardized service.

## Features

- **🔌 Standard MCP Protocol**: Full compliance with MCP specification for universal client compatibility
- **🤖 Multi-Model Integration**: Supports 5 major Chinese AI models:
  - 通义千问 (Qwen) - Alibaba's large language model
  - 智谱清言 (Zhipu) - GLM-4 model
  - 文心一言 (Baidu) - <PERSON>
  - DeepSeek - DeepSeek Chat model
  - Kimi - Moonshot AI model

- **🧠 Automatic Question Classification**: Uses a lightweight model to automatically categorize questions into:
  - `technical` - Technical questions (programming, engineering, science)
  - `creative` - Creative questions (writing, design, brainstorming)
  - `analytical` - Analytical questions (data analysis, logical reasoning, math)
  - `coding` - Programming/code-specific questions
  - `long_text` - Long text processing questions
  - `default` - General questions

- **⚡ Strategy-Based Model Selection**: Different question types use different combinations of models for optimal results
- **🚀 Concurrent Processing**: Uses ThreadPoolExecutor to query multiple models simultaneously for faster responses
- **📝 Response Summarization**: Automatically generates a comprehensive summary from all model responses
- **🔧 Standard MCP Transport**: Uses stdio transport for universal MCP client compatibility

## MCP Components

### Tools
- `ask_question` - Main multi-model question answering tool
- `classify_question_tool` - Question classification tool
- `call_single_model` - Single model calling tool
- `list_available_models` - List available models
- `list_question_strategies` - List question classification strategies

### Resources
- `config://models` - Model configurations
- `config://strategies` - Question classification strategies
- `config://classifier-prompt` - Question classifier prompt template
- `status://models` - Model status and health information

### Prompts
- `question_classifier` - Question classification prompt template
- `response_summarizer` - Response summarization prompt template
- `model_selector` - Model selection recommendation prompt

## Prerequisites

- Python 3.8+
- MCP Python SDK
- Requests library

## Installation

1. Clone the repository
2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

## Configuration

Set up your API keys as environment variables:

```bash
export QWEN_API_KEY=your_qwen_api_key
export ZHIPU_API_KEY=your_zhipu_api_key
export BAIDU_API_KEY=your_baidu_api_key
export DEEPSEEK_API_KEY=your_deepseek_api_key
export KIMI_API_KEY=your_kimi_api_key
```

Or modify the [config.py](config.py) file directly.

## Usage

### Quick Start

Use the provided startup script:

```bash
# Start the MCP server
./start_mcp_server.sh
```

### Manual Start

```bash
# Start the MCP server with stdio transport
python mcp_server.py

# Test configuration
python mcp_server.py --test

# Enable debug mode
python mcp_server.py --debug
```

### Configuration Validation

Before using the server, validate your configuration:

```bash
# Check API keys and test model connectivity
python mcp_server.py --test
```

This will:
- Check if all required API keys are configured
- Test connectivity to available models
- Validate the configuration setup

### Using with MCP Clients

#### Claude Desktop (stdio transport)

Add to your Claude Desktop configuration:

```json
{
  "mcpServers": {
    "multi-model-qa": {
      "command": "python",
      "args": ["/opt/mchat/mcp_server.py"],
      "env": {
        "QWEN_API_KEY": "your_qwen_api_key",
        "ZHIPU_API_KEY": "your_zhipu_api_key",
        "BAIDU_API_KEY": "your_baidu_api_key",
        "DEEPSEEK_API_KEY": "your_deepseek_api_key",
        "KIMI_API_KEY": "your_kimi_api_key"
      }
    }
  }
}
```

#### Testing the MCP Server

You can test the server functionality using the provided test scripts:

```bash
# Test basic MCP functionality
python test_mcp_server.py

# Test with example questions
python example_usage.py
```

#### Other MCP Clients

Use the provided `mcp_config.json` as a template for other MCP clients.

### MCP Tool Usage Examples

Once connected to an MCP client, you can use the following tools:

#### Ask a Question (Multi-Model)
```
Use the ask_question tool with:
- question: "用Python写一个快速排序算法"
- question_type: "coding" (optional)
```

#### Classify a Question
```
Use the classify_question_tool with:
- question: "What's the best way to optimize database queries?"
```

#### Call a Single Model
```
Use the call_single_model tool with:
- model_key: "qwen"
- prompt: "Explain quantum computing in simple terms"
```

## How It Works

### stdio Transport (Traditional MCP)
1. **MCP Client Connection**: The server accepts connections from MCP clients via stdio transport
2. **Question Classification**: When a question is received, the system uses a small model (Qwen-turbo) to classify the question type
3. **Model Selection**: Based on the classification result, the system selects an appropriate combination of models
4. **Concurrent Processing**: The question is sent to multiple models simultaneously using ThreadPoolExecutor
5. **Response Aggregation**: Responses from all models are collected and summarized by another model
6. **MCP Response**: The result is returned to the MCP client in the standard format



### Message Format
All MCP messages follow JSON-RPC 2.0 specification:
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "ask_question",
    "arguments": {
      "question": "Your question here",
      "question_type": "optional_type"
    }
  }
}
```

## Supported Models

1. **通义千问 (Qwen)** - From Alibaba Cloud
2. **智谱清言 (Zhipu AI)** - From Zhipu AI
3. **文心一言 (Ernie Bot)** - From Baidu
4. **DeepSeek** - From DeepSeek
5. **Kimi** - From Moonshot AI

## Strategies

- **default**: Qwen + Zhipu (for general questions)
- **technical**: Qwen + Zhipu + Baidu (for technical questions)
- **creative**: Qwen + Zhipu (for creative questions)
- **analytical**: Qwen + Baidu (for analytical questions)
- **coding**: Qwen + DeepSeek + Kimi (for code-related questions)
- **long_text**: Qwen + Kimi (for long text processing)

The system automatically selects the appropriate strategy based on the question content. You can also manually specify a strategy by using the corresponding endpoint.

## Configuration and Customization

### Adding a New Model

To add a new model to the MCP server:

1. Add the model configuration in [config.py](config.py):
   ```python
   MODEL_CONFIGS = {
       # ... existing models ...
       "new_model": {
           "name": "New Model Name",
           "api_url": "https://api.newmodel.com/v1/chat/completions",
           "api_key": os.environ.get("NEW_MODEL_API_KEY", "your_new_model_api_key"),
           "model": "new-model-version",
       }
   }
   ```

2. Update the `get_headers` function in [mcp_server.py](mcp_server.py) to handle authentication for the new model:
   ```python
   if model_key in ["qwen", "summary", "deepseek", "kimi", "classifier", "new_model"]:
       headers["Authorization"] = f"Bearer {config['api_key']}"
   ```

3. Update the response parsing logic in the `call_model` function in [mcp_server.py](mcp_server.py) if needed:
   ```python
   if model_key in ["qwen", "summary", "deepseek", "kimi", "classifier", "new_model"]:
       content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
   ```

### Adding a New Strategy

To add a new strategy for a specific question type:

1. Add the strategy in [config.py](config.py):
   ```python
   STRATEGY_CONFIG = {
       # ... existing strategies ...
       "new_strategy": ["qwen", "new_model"]
   }
   ```

2. Optionally, update the classifier prompt in [config.py](config.py) to include the new question type:
   ```python
   CLASSIFIER_PROMPT = """...
   n. new_strategy - Description of when to use this strategy
   ..."""
   ```

## Legacy HTTP API Support

The original Flask-based HTTP API (`app.py`) is still available for backward compatibility. However, we recommend using the MCP server for new integrations.

### CherryStudio Integration (Legacy)

For CherryStudio integration, you can still use the OpenAI-compatible adapter:

1. Start the legacy HTTP service and adapter:
   ```bash
   # Terminal 1: Start the legacy HTTP service
   python app.py

   # Terminal 2: Start the CherryStudio adapter
   python cherry_studio_adapter.py
   ```

2. Configure CherryStudio with:
   - API Endpoint: `http://localhost:8000/v1`
   - Model Name: `mcp-multi-model`

## Development and Testing

### Running Tests

#### stdio Transport Tests
```bash
# Test the MCP server with stdio transport
python mcp_server.py

# Test with MCP client tools
python test_mcp_client.py
```



### Web Client Testing
```bash
# Open the web client in your browser
open simple_web_client.html

# Or use the full-featured web client
open web_client.html
```

### Debugging

Enable debug logging by setting the environment variable:
```bash
export MCP_DEBUG=1
```

## MCP Response Format

The MCP server returns structured responses through the MCP protocol:

```python
{
  "question": "The question asked",
  "question_type": "auto or manually specified type",
  "models_used": ["List of model keys used"],
  "responses": [
    {
      "model": "Model name",
      "response": "Model's response",
      "status": "success or error",
      "error": "Error message if status is error"
    }
  ],
  "summary": "Comprehensive summary of all responses"
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with stdio transport
5. Submit a pull request

## License

This project is open source and available under the MIT License.