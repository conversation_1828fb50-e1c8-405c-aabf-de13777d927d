#!/bin/bash

# Multi-Model Question Answering MCP Server Startup Script

set -e

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Check environment variables
echo "Checking environment variables..."
required_vars=("QWEN_API_KEY" "ZHIPU_API_KEY" "BAIDU_API_KEY" "DEEPSEEK_API_KEY" "KIMI_API_KEY")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "Warning: The following environment variables are not set:"
    printf '%s\n' "${missing_vars[@]}"
    echo ""
    echo "Please set them before running the server:"
    for var in "${missing_vars[@]}"; do
        echo "export $var=your_${var,,}_here"
    done
    echo ""
fi

# Parse command line arguments
DEBUG=""
TEST=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            DEBUG="--debug"
            shift
            ;;
        --test)
            TEST="--test"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --debug               Enable debug mode"
            echo "  --test                Run configuration validation"
            echo "  -h, --help            Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Start MCP server with stdio transport"
            echo "  $0 --debug            # Start with debug mode enabled"
            echo "  $0 --test             # Validate configuration and test connectivity"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Start the MCP server
echo "Starting Multi-Model Question Answering MCP Server..."
echo "Transport: stdio (Standard Input/Output)"
echo ""

python mcp_server.py $DEBUG $TEST
