import os

class Config:
    # Model configurations
    MODEL_CONFIGS = {
        "qwen": {
            "name": "通义千问",
            "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
            "api_key": os.environ.get("QWEN_API_KEY", "your_qwen_api_key"),
            "model": "qwen-plus",
        },
        "zhipu": {
            "name": "智谱清言",
            "api_url": "https://open.bigmodel.cn/api/paas/v4/chat/completions",
            "api_key": os.environ.get("ZHIPU_API_KEY", "your_zhipu_api_key"),
            "model": "glm-4",
        },
        "baidu": {
            "name": "文心一言",
            "api_url": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions",
            "api_key": os.environ.get("BAIDU_API_KEY", "your_baidu_api_key"),
            "model": "ernie-bot-turbo",
        },
        "deepseek": {
            "name": "DeepSeek",
            "api_url": "https://api.deepseek.com/v1/chat/completions",
            "api_key": os.environ.get("DEEPSEEK_API_KEY", "your_deepseek_api_key"),
            "model": "deepseek-chat",
        },
        "kimi": {
            "name": "Kimi",
            "api_url": "https://api.moonshot.cn/v1/chat/completions",
            "api_key": os.environ.get("KIMI_API_KEY", "your_kimi_api_key"),
            "model": "moonshot-v1-8k",
        },
        "classifier": {
            "name": "问题分类器",
            "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
            "api_key": os.environ.get("QWEN_API_KEY", "your_qwen_api_key"),
            "model": "qwen-turbo",
        },
        "summary": {
            "name": "总结模型",
            "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
            "api_key": os.environ.get("QWEN_API_KEY", "your_qwen_api_key"),  # Default to Qwen for summary
            "model": "qwen-plus",
        }
    }

    # Strategy configuration for question types
    STRATEGY_CONFIG = {
        "default": ["qwen", "zhipu"],
        "technical": ["qwen", "zhipu", "baidu"],
        "creative": ["qwen", "zhipu"],
        "analytical": ["qwen", "baidu"],
        "coding": ["qwen", "deepseek", "kimi"],
        "long_text": ["qwen", "kimi"]
    }

    # Question classifier prompt template
    CLASSIFIER_PROMPT = """请根据问题的内容和特点，将其分类到以下类别之一：

类别选项：
1. technical - 技术类问题（编程、工程、科学等技术相关）
2. creative - 创意类问题（写作、设计、头脑风暴等创意相关）
3. analytical - 分析类问题（数据分析、逻辑推理、数学计算等）
4. coding - 编程代码类问题（具体需要写代码或解释代码）
5. long_text - 长文本处理问题（需要处理大量文本或生成长文）
6. default - 其他通用问题

请只回答类别名称（如 technical、creative等），不要添加其他内容。

问题：{question}"""