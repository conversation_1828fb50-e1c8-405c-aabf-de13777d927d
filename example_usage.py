#!/usr/bin/env python3
"""
Example usage of the Multi-Model Question Answering MCP Server

This script demonstrates how to interact with the MCP server programmatically.
Note: This is for demonstration purposes. In practice, you would use an MCP client.
"""

import json
from mcp_server import (
    ask_question, classify_question_tool, call_single_model,
    list_available_models, list_question_strategies,
    get_models_config, get_strategies_config, get_classifier_prompt,
    QuestionRequest
)

def demo_tools():
    """Demonstrate MCP tools functionality."""
    print("🔧 MCP Tools Demo")
    print("=" * 50)
    
    # List available models
    print("\n📋 Available Models:")
    models = list_available_models()
    for key, name in models.items():
        print(f"  - {key}: {name}")
    
    # List question strategies
    print("\n📋 Question Strategies:")
    strategies = list_question_strategies()
    for strategy, models in strategies.items():
        print(f"  - {strategy}: {', '.join(models)}")
    
    # Classify a question
    print("\n🧠 Question Classification:")
    test_question = "用Python写一个快速排序算法"
    classification = classify_question_tool(test_question)
    print(f"  Question: {test_question}")
    print(f"  Classification: {classification}")
    
    print("\n✅ Tools demo completed!")

def demo_resources():
    """Demonstrate MCP resources functionality."""
    print("\n📚 MCP Resources Demo")
    print("=" * 50)
    
    # Get models configuration
    print("\n⚙️ Models Configuration:")
    models_config = get_models_config()
    config_data = json.loads(models_config)
    print(f"  Found {len(config_data)} model configurations")
    for key in list(config_data.keys())[:3]:  # Show first 3
        print(f"  - {key}: {config_data[key]['name']}")
    
    # Get strategies configuration
    print("\n⚙️ Strategies Configuration:")
    strategies_config = get_strategies_config()
    strategies_data = json.loads(strategies_config)
    print(f"  Found {len(strategies_data)} strategies")
    
    # Get classifier prompt
    print("\n⚙️ Classifier Prompt:")
    prompt = get_classifier_prompt()
    print(f"  Prompt length: {len(prompt)} characters")
    print(f"  Preview: {prompt[:100]}...")
    
    print("\n✅ Resources demo completed!")

def demo_prompts():
    """Demonstrate MCP prompts functionality."""
    print("\n📝 MCP Prompts Demo")
    print("=" * 50)
    
    from mcp_server import question_classifier, response_summarizer, model_selector
    
    # Question classifier prompt
    print("\n🧠 Question Classifier Prompt:")
    test_question = "如何优化数据库查询性能？"
    classifier_prompt = question_classifier(test_question)
    print(f"  Generated prompt for: {test_question}")
    print(f"  Prompt length: {len(classifier_prompt)} characters")
    
    # Response summarizer prompt
    print("\n📊 Response Summarizer Prompt:")
    test_responses = "模型A: 答案A\n模型B: 答案B"
    summarizer_prompt = response_summarizer(test_question, test_responses)
    print(f"  Generated summarizer prompt")
    print(f"  Prompt length: {len(summarizer_prompt)} characters")
    
    # Model selector prompt
    print("\n🎯 Model Selector Prompt:")
    selector_prompt = model_selector(test_question, "technical")
    print(f"  Generated selector prompt for technical question")
    print(f"  Prompt length: {len(selector_prompt)} characters")
    
    print("\n✅ Prompts demo completed!")

def demo_mock_usage():
    """Demonstrate mock usage (without actual API calls)."""
    print("\n🎭 Mock Usage Demo")
    print("=" * 50)
    
    print("\n⚠️  Note: This demo uses mock data since API keys are not configured.")
    print("In a real environment with API keys, the MCP server would:")
    print("1. Classify the question automatically")
    print("2. Select appropriate models based on question type")
    print("3. Call multiple models concurrently")
    print("4. Summarize all responses into a comprehensive answer")
    
    # Example of what the response would look like
    example_response = {
        "question": "用Python写一个快速排序算法",
        "question_type": "coding",
        "models_used": ["qwen", "deepseek", "kimi"],
        "responses": [
            {
                "model": "通义千问",
                "response": "这是一个快速排序的Python实现...",
                "status": "success"
            },
            {
                "model": "DeepSeek",
                "response": "快速排序算法的Python代码如下...",
                "status": "success"
            },
            {
                "model": "Kimi",
                "response": "以下是快速排序的详细实现...",
                "status": "success"
            }
        ],
        "summary": "综合多个模型的回答，快速排序是一种高效的排序算法..."
    }
    
    print("\n📄 Example Response Structure:")
    print(json.dumps(example_response, indent=2, ensure_ascii=False))
    
    print("\n✅ Mock usage demo completed!")

def main():
    """Run all demonstrations."""
    print("🚀 Multi-Model Question Answering MCP Server Demo")
    print("=" * 60)
    
    try:
        demo_tools()
        demo_resources()
        demo_prompts()
        demo_mock_usage()
        
        print("\n" + "=" * 60)
        print("🎉 All demos completed successfully!")
        print("\n📖 Next Steps:")
        print("1. Set up your API keys in environment variables")
        print("2. Start the MCP server: python mcp_server.py")
        print("3. Connect with an MCP client (like Claude Desktop)")
        print("4. Start asking questions and get multi-model responses!")
        
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
