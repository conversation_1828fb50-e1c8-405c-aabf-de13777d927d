#!/usr/bin/env python3
"""
Multi-Model Question Answering MCP Server

This MCP server provides tools for querying multiple Chinese AI models simultaneously,
with automatic question classification and response summarization.
"""

import asyncio
import json
import os
import requests
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

from mcp.server.fastmcp import FastMCP
from mcp.server.fastmcp.prompts import base
from config import Config


# Create the MCP server
mcp = FastMCP("Multi-Model Question Answering Server")


class QuestionRequest(BaseModel):
    """Request model for asking questions."""
    question: str = Field(description="The question to ask")
    question_type: Optional[str] = Field(
        default=None, 
        description="Optional question type (technical, creative, analytical, coding, long_text, default)"
    )


class ModelResponse(BaseModel):
    """Response from a single model."""
    model: str = Field(description="Model name")
    response: str = Field(description="Model's response")
    status: str = Field(description="Response status (success/error)")
    error: Optional[str] = Field(default=None, description="Error message if status is error")


class MultiModelResponse(BaseModel):
    """Response from multiple models with summary."""
    question: str = Field(description="The original question")
    question_type: str = Field(description="Classified or specified question type")
    models_used: List[str] = Field(description="List of model keys used")
    responses: List[ModelResponse] = Field(description="Individual model responses")
    summary: str = Field(description="Summarized response from all models")


def get_headers(model_key: str) -> Dict[str, str]:
    """Generate appropriate headers for each model."""
    config = Config.MODEL_CONFIGS.get(model_key)
    if not config:
        return {}
    
    headers = {"Content-Type": "application/json"}
    
    if model_key in ["qwen", "summary", "deepseek", "kimi", "classifier"]:
        headers["Authorization"] = f"Bearer {config['api_key']}"
    elif model_key == "zhipu":
        headers["Authorization"] = f"Bearer {config['api_key']}"
    elif model_key == "baidu":
        # Baidu uses token in URL
        pass
    
    return headers


def call_model(model_key: str, prompt: str) -> Dict[str, Any]:
    """Call a specific model with the given prompt."""
    config = Config.MODEL_CONFIGS.get(model_key)
    if not config:
        return {"model": model_key, "error": "Model configuration not found", "status": "error"}
    
    try:
        payload = {
            "model": config["model"],
            "messages": [{"role": "user", "content": prompt}]
        }
        
        url = config["api_url"]
        if model_key == "baidu":
            url = f"{config['api_url']}?access_token={config['api_key']}"
        
        response = requests.post(
            url,
            headers=get_headers(model_key),
            data=json.dumps(payload),
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = ""
            
            if model_key in ["qwen", "summary", "deepseek", "kimi", "classifier"]:
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            elif model_key == "zhipu":
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            elif model_key == "baidu":
                content = result.get("result", "")
            
            return {
                "model": config["name"],
                "response": content,
                "status": "success"
            }
        else:
            return {
                "model": config["name"],
                "error": f"API call failed with status {response.status_code}",
                "response": response.text,
                "status": "error"
            }
            
    except Exception as e:
        return {
            "model": config["name"],
            "error": str(e),
            "status": "error"
        }


def classify_question(question: str) -> str:
    """Use a small model to classify the question type."""
    prompt = Config.CLASSIFIER_PROMPT.format(question=question)
    result = call_model("classifier", prompt)
    
    if result.get("status") == "success":
        classification = result.get("response", "").strip().lower()
        if classification in Config.STRATEGY_CONFIG:
            return classification
    
    return "default"


def get_models_for_question(question: str, question_type: Optional[str] = None) -> List[str]:
    """Determine which models to use based on question type."""
    if question_type and question_type in Config.STRATEGY_CONFIG:
        return Config.STRATEGY_CONFIG[question_type]
    else:
        auto_type = classify_question(question)
        return Config.STRATEGY_CONFIG[auto_type]


def summarize_responses(question: str, responses: List[Dict[str, Any]]) -> str:
    """Summarize the responses from multiple models."""
    response_texts = []
    for resp in responses:
        if resp.get("status") == "success":
            response_texts.append(f"模型 {resp['model']} 的回答:\n{resp['response']}\n")
        else:
            response_texts.append(f"模型 {resp['model']} 出现错误: {resp.get('error', 'Unknown error')}\n")
    
    summary_prompt = f"请根据以下多个模型对问题 '{question}' 的回答进行总结:\n\n" + "\n".join(response_texts) + "\n请给出一个综合性的总结回答:"
    
    summary_result = call_model("summary", summary_prompt)
    if summary_result.get("status") == "success":
        return summary_result.get("response", "")
    else:
        error_msg = summary_result.get("error", "Unknown error")
        return f"总结生成失败: {error_msg}"


@mcp.tool()
def ask_question(request: QuestionRequest) -> MultiModelResponse:
    """
    Ask a question to multiple AI models simultaneously and get a summarized response.
    
    This tool automatically classifies the question type (unless specified) and selects
    the most appropriate combination of AI models to provide comprehensive answers.
    """
    question = request.question
    question_type = request.question_type
    
    # Determine which models to use
    model_keys = get_models_for_question(question, question_type)
    
    # Use ThreadPoolExecutor to call models concurrently
    with ThreadPoolExecutor(max_workers=len(model_keys)) as executor:
        future_to_model = {
            executor.submit(call_model, model_key, question): model_key 
            for model_key in model_keys
        }
        
        responses = []
        for future in future_to_model:
            try:
                result = future.result()
                responses.append(result)
            except Exception as e:
                model_key = future_to_model[future]
                responses.append({
                    "model": Config.MODEL_CONFIGS.get(model_key, {}).get("name", model_key),
                    "error": str(e),
                    "status": "error"
                })
    
    # Generate summary
    summary = summarize_responses(question, responses)
    
    # Convert responses to ModelResponse objects
    model_responses = [
        ModelResponse(
            model=resp["model"],
            response=resp.get("response", ""),
            status=resp["status"],
            error=resp.get("error")
        )
        for resp in responses
    ]
    
    return MultiModelResponse(
        question=question,
        question_type=question_type or "auto",
        models_used=model_keys,
        responses=model_responses,
        summary=summary
    )


@mcp.tool()
def classify_question_tool(question: str) -> str:
    """
    Classify a question into one of the predefined categories.
    
    Categories: technical, creative, analytical, coding, long_text, default
    """
    return classify_question(question)


@mcp.tool()
def call_single_model(model_key: str, prompt: str) -> ModelResponse:
    """
    Call a single AI model with a specific prompt.
    
    Available models: qwen, zhipu, baidu, deepseek, kimi
    """
    if model_key not in Config.MODEL_CONFIGS:
        return ModelResponse(
            model=model_key,
            response="",
            status="error",
            error=f"Unknown model: {model_key}. Available models: {list(Config.MODEL_CONFIGS.keys())}"
        )
    
    result = call_model(model_key, prompt)
    return ModelResponse(
        model=result["model"],
        response=result.get("response", ""),
        status=result["status"],
        error=result.get("error")
    )


@mcp.tool()
def list_available_models() -> Dict[str, str]:
    """List all available AI models and their descriptions."""
    return {
        k: v["name"] for k, v in Config.MODEL_CONFIGS.items() 
        if k not in ["summary", "classifier"]
    }


@mcp.tool()
def list_question_strategies() -> Dict[str, List[str]]:
    """List all question classification strategies and their associated models."""
    return Config.STRATEGY_CONFIG


# MCP Resources
@mcp.resource("config://models")
def get_models_config() -> str:
    """Get the complete models configuration."""
    return json.dumps(Config.MODEL_CONFIGS, indent=2, ensure_ascii=False)


@mcp.resource("config://strategies")
def get_strategies_config() -> str:
    """Get the question classification strategies configuration."""
    return json.dumps(Config.STRATEGY_CONFIG, indent=2, ensure_ascii=False)


@mcp.resource("config://classifier-prompt")
def get_classifier_prompt() -> str:
    """Get the question classifier prompt template."""
    return Config.CLASSIFIER_PROMPT


@mcp.resource("status://models")
def get_models_status() -> str:
    """Get the current status and health of all models."""
    status = {}
    for model_key, config in Config.MODEL_CONFIGS.items():
        if model_key in ["summary", "classifier"]:
            continue

        # Simple health check - try to get model info
        try:
            # For a real health check, you might want to make a simple API call
            # Here we just check if the API key is configured
            api_key = config.get("api_key", "")
            if api_key and api_key != f"your_{model_key}_api_key":
                status[model_key] = {
                    "name": config["name"],
                    "status": "configured",
                    "api_url": config["api_url"],
                    "model": config["model"]
                }
            else:
                status[model_key] = {
                    "name": config["name"],
                    "status": "not_configured",
                    "error": "API key not set"
                }
        except Exception as e:
            status[model_key] = {
                "name": config["name"],
                "status": "error",
                "error": str(e)
            }

    return json.dumps(status, indent=2, ensure_ascii=False)


# MCP Prompts
@mcp.prompt()
def question_classifier(question: str) -> str:
    """
    Prompt template for classifying questions into categories.

    Args:
        question: The question to classify
    """
    return Config.CLASSIFIER_PROMPT.format(question=question)


@mcp.prompt()
def response_summarizer(question: str, responses: str) -> str:
    """
    Prompt template for summarizing multiple model responses.

    Args:
        question: The original question
        responses: Formatted responses from multiple models
    """
    return f"请根据以下多个模型对问题 '{question}' 的回答进行总结:\n\n{responses}\n请给出一个综合性的总结回答:"


@mcp.prompt()
def model_selector(question: str, question_type: str = "unknown") -> str:
    """
    Prompt template for suggesting which models to use for a question.

    Args:
        question: The question to analyze
        question_type: The classified question type (optional)
    """
    available_strategies = json.dumps(Config.STRATEGY_CONFIG, indent=2, ensure_ascii=False)

    return f"""基于以下问题和可用的模型策略，建议使用哪些模型组合：

问题: {question}
问题类型: {question_type}

可用策略:
{available_strategies}

请分析问题特点并推荐最适合的模型组合策略。"""


if __name__ == "__main__":
    import sys
    import argparse

    parser = argparse.ArgumentParser(description="Multi-Model Question Answering MCP Server")
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    parser.add_argument(
        "--test",
        action="store_true",
        help="Run in test mode (validate configuration)"
    )

    args = parser.parse_args()

    if args.debug:
        import logging
        logging.basicConfig(level=logging.DEBUG)
        print("🐛 Debug mode enabled", file=sys.stderr)

    if args.test:
        print("🧪 Running configuration validation...", file=sys.stderr)

        # Test API keys
        missing_keys = []
        for model_key, config in Config.MODEL_CONFIGS.items():
            if model_key in ["summary", "classifier"]:
                continue
            api_key = config.get("api_key")
            if not api_key or api_key.startswith("your_"):
                missing_keys.append(f"{model_key}: {config['name']}")

        if missing_keys:
            print("⚠️ Missing API keys for:", file=sys.stderr)
            for key in missing_keys:
                print(f"  - {key}", file=sys.stderr)
            print("💡 Set environment variables or update config.py", file=sys.stderr)
        else:
            print("✅ All API keys configured", file=sys.stderr)

        # Test model availability
        print("🔍 Testing model connectivity...", file=sys.stderr)
        for model_key in ["qwen", "zhipu"]:  # Test a couple of models
            if Config.MODEL_CONFIGS[model_key]["api_key"].startswith("your_"):
                continue
            try:
                result = call_model(model_key, "Hello")
                if result["status"] == "success":
                    print(f"✅ {Config.MODEL_CONFIGS[model_key]['name']}: Connected", file=sys.stderr)
                else:
                    print(f"❌ {Config.MODEL_CONFIGS[model_key]['name']}: {result.get('error', 'Unknown error')}", file=sys.stderr)
            except Exception as e:
                print(f"❌ {Config.MODEL_CONFIGS[model_key]['name']}: {str(e)}", file=sys.stderr)

        print("🎯 Configuration validation complete", file=sys.stderr)
        sys.exit(0)

    # Run the MCP server with stdio transport
    print("🚀 Starting Multi-Model Question Answering MCP Server", file=sys.stderr)
    print("📡 Transport: stdio (Standard Input/Output)", file=sys.stderr)
    print("🔧 Available tools: ask_question, list_available_models, classify_question_tool, call_single_model, list_question_strategies", file=sys.stderr)
    print("📚 Available resources: config://models, config://strategies, config://classifier-prompt, status://models", file=sys.stderr)
    print("📝 Available prompts: question_classifier, response_summarizer, model_selector", file=sys.stderr)
    print("=" * 80, file=sys.stderr)

    try:
        mcp.run()
    except KeyboardInterrupt:
        print("\n👋 MCP Server stopped", file=sys.stderr)
    except Exception as e:
        print(f"❌ Error running MCP server: {e}", file=sys.stderr)
        sys.exit(1)
